<template>
  <div>
    <!-- 搜索和操作栏 -->
      <el-form :inline="true" :model="search" ref="searchForm">
        <el-form-item label="产品名称:" prop="systemName">
          <el-input v-model="search.systemName" placeholder="请输入" maxlength="100" />
        </el-form-item>
        <el-form-item label="产品标识:" prop="systemCode">
          <el-input v-model="search.systemCode" placeholder="请选择" maxlength="100" />
        </el-form-item>

        <el-form-item label="产品类别:" prop="systemType" >
          <el-select v-model="search.systemType" placeholder="请选择" filterable :empty-values="[null, undefined]" >
            <el-option label="全部" value="" />
            <el-option v-for="(item, index) in systemTypeEnum" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属机构:" prop="organizationId">
          <el-select v-model="search.organizationId" placeholder="请选择" filterable :empty-values="[null, undefined]" >
            <el-option label="全部" value="" />
            <el-option  v-for="(item, index) in state.organizationOptions" :key="index" :label="item.organizationName" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="负责项目经理:" prop="projectManager" >
          <el-select v-model="search.projectManager" placeholder="请选择" filterable :empty-values="[null, undefined]">
            <el-option label="全部" value="" />
            <el-option v-for="(item, index) in projectManageOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责开发组:" prop="devTeam">
          <el-select v-model="search.devTeam" placeholder="请选择"  filterable :empty-values="[null, undefined]">
            <el-option label="全部" value="" />
            <el-option v-for="(item, index) in devTeamOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="负责测试组:" prop="testTeam">
          <el-select v-model="search.testTeam" placeholder="请选择" filterable :empty-values="[null, undefined]">
            <el-option label="全部" value="" />
            <el-option v-for="(item, index) in testTeamOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadData(1)">查询<el-icon class="el-icon--right"><Search /></el-icon></el-button>
          <el-button @click="handleReset(searchForm)">重置<el-icon class="el-icon--right"><RefreshLeft /></el-icon></el-button>
        </el-form-item>
      </el-form>

    <!-- 数据表格 -->
      <el-table :data="state.tableData" border stripe v-loading="loading" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }">
        <el-table-column prop="systemName" label="产品名称" width="200" />
        <el-table-column prop="systemNo" label="产品编号" width="250" />
        <el-table-column prop="systemCode" label="产品标识" width="200" />
        <el-table-column prop="systemType" label="产品类别" width="150">
          <template #default="{ row }">
            <span>{{ row.systemType ? optionDeptVal(row.systemType, systemTypeEnum) : row.systemType}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="appNum" label="应用数量" width="100" />
        <el-table-column prop="organizationName" label="所属机构" width="150" />
        <el-table-column prop="projectManager" label="负责项目经理" width="130" />
        <el-table-column prop="devTeam" label="负责开发组" width="130" />
        <el-table-column prop="testTeam" label="负责测试组" width="130" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary"  @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>

    <!-- 新增/编辑弹窗 -->
    <addEditDialog v-model:dialogVisible="addEditDialog.show" :dialogForm="addEditDialog.row" :organizationOptions = "state.organizationOptions" @callback="addEditDialog.callback"></addEditDialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {ElMessage, type FormInstance} from 'element-plus'
import {Search, RefreshLeft} from '@element-plus/icons-vue'
import { getProductPage} from '@/api/ProductManagement'
import { getOrganizationPage} from '@/api/OrganizationManagement'
import AddEditDialog from './components/ProductManageDialog.vue'
import {type dictItemValue, dictList} from "@/api/CommApi";
const searchForm = ref<FormInstance>()

const devTeamOptions = ref<dictItemValue[]>([])
const testTeamOptions = ref<dictItemValue[]>([])
const projectManageOptions = ref<dictItemValue[]>([])
const systemTypeEnum = ref<dictItemValue[]>([])

/* 搜索表单 */
const search = reactive({
  systemName: '',
  systemCode: '',
  organizationId: '',
  systemType: '',
  testTeam: '',
  devTeam: '',
  projectManager: '',
  orderFields: 'createTime',
  orderRules: 'desc'
})

/* 表格 & 分页 */
let state = reactive({
  tableData: [] as any,
  organizationOptions: [] as any
})
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})


/* 弹窗 */

/* 加载表格数据 */
const loadData = async (page = 1) => {
  const params = {
    pageNum: page || 1,
    pageSize: pagination.pageSize,
    systemName: search.systemName,
    systemCode: search.systemCode,
    organizationId: search.organizationId,
    systemType: search.systemType,
    testTeam: search.testTeam,
    devTeam: search.devTeam,
    projectManager: search.projectManager,
  }
  loading.value = true
    getProductPage(params).then((res: any) => {
      loading.value = false
      const {code, data, message} = res || {}
      if (code === '0000') {
        state.tableData = data.records || []
        pagination.total = data.total || 0
        pagination.currentPage = res.data.current
      } else {
        ElMessage({message: message, type: 'error'})
      }
    })
}

const getOrganizationOption = async () => {
  getOrganizationPage({}).then((res: any) => {
    loading.value = false
    const {code, data, message} = res || {}
    if (code === '0000') {
      state.organizationOptions = data.records
    } else {
      ElMessage({message: message, type: 'error'})
    }
  })
}

// 新增编辑字典弹窗
const addEditDialog = reactive({
  show: false,
  row: {},
  organizationOptions: [{}],
  callback: (data: any) => {}
})
// 重置表单
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const handleEdit = (row: any) => {
  addEditDialog.row = row
  addEditDialog.show = true
  addEditDialog.callback = (data: any) => {
    loadData(1)
  }
}

/* 分页事件 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData(1)
}
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData(page)
}

/* 弹窗成功回调 */
const handleFormSuccess = () => {
  loadData()
}

const getDictOptions = () => {
  dictList('app_dev_dept').then((res: any) => {
    const { code, data, message } = res || {}
    if (code === '0000') {
      devTeamOptions.value = data
    } else {
      ElMessage.error(message)
    }
  })
  dictList('app_test_dept').then((res: any) => {
    const { code, data, message } = res || {}
    if (code === '0000') {
      testTeamOptions.value = data
    } else {
      ElMessage.error(message)
    }
  })
  dictList('app_project_manage').then((res: any) => {
    const { code, data, message } = res || {}
    if (code === '0000') {
      projectManageOptions.value = data
    } else {
      ElMessage.error(message)
    }
  })
  dictList('system_type').then((res: any) => {
    const { code, data, message } = res || {}
    if (code === '0000') {
      systemTypeEnum.value = data
    } else {
      ElMessage.error(message)
    }
  })
}

// 构建结果枚举匹配
const optionDeptVal = (val: any, obj: any[]) => {
  console.log(obj)
  const arr = obj.filter((item) => item.value == val)[0]
  return arr ? arr.label : val
}

onMounted(() => {
  loadData()
  getOrganizationOption()
  getDictOptions()
})
</script>

<style lang="scss" scoped>
.el-input {
  width: 220px;
}
.el-select {
  width: 220px;
}
.el-message-box__container {
  align-items: normal;
}
.ellipsis {
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>